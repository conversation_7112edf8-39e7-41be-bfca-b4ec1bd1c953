import win32com.client
import time
import os
import math
import pythoncom
# from collections import defaultdict  # 不再需要


def get_polyline_endpoints(polyline):
    """获取多段线的起点和终点坐标"""
    try:
        # 获取所有坐标
        coords_variant = polyline.Coordinates

        # 将VARIANT转换为Python列表
        coords = list(coords_variant)

        # 坐标是以 [x1, y1, x2, y2, ...] 格式存储的
        if len(coords) < 4:  # 至少需要2个点（4个坐标值）
            return None, None

        # 获取起点
        start_point = (coords[0], coords[1])

        # 获取终点（最后一个点）
        end_point = (coords[-2], coords[-1])

        return start_point, end_point

    except Exception as e:
        print(f"获取端点坐标时出错: {str(e)}")
        try:
            # 备用方法：使用GetBoundingBox
            min_point = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_R8, [0.0, 0.0, 0.0])
            max_point = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_R8, [0.0, 0.0, 0.0])
            polyline.GetBoundingBox(min_point, max_point)

            # 这只是一个粗略的估计，不是精确的端点
            return None, None
        except:
            return None, None


def get_all_vertices(polyline):
    """获取多段线的所有顶点坐标"""
    vertices = []
    try:
        # 获取所有坐标
        coords_variant = polyline.Coordinates
        coords = list(coords_variant)

        # 将坐标列表转换为点列表
        # 坐标是以 [x1, y1, x2, y2, ...] 格式存储的
        for i in range(0, len(coords), 2):
            if i + 1 < len(coords):
                vertices.append((coords[i], coords[i + 1]))

        return vertices

    except Exception as e:
        print(f"获取顶点坐标时出错: {str(e)}")
        return []


def points_are_close(p1, p2, tolerance=0.001):
    """判断两个点是否足够接近"""
    if p1 is None or p2 is None:
        return False

    distance = math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2)
    return distance < tolerance


def find_connected_segments(polylines, tolerance=0.001):
    """找出所有连通的线段组 - 使用链式连接方法"""
    # 获取所有线段的端点
    segments = []
    for i, polyline in enumerate(polylines):
        start, end = get_polyline_endpoints(polyline)
        if start and end:
            segments.append({
                'index': i,
                'polyline': polyline,
                'start': start,
                'end': end,
                'vertices': get_all_vertices(polyline),
                'used': False
            })

    groups = []

    # 对每个未使用的线段，尝试构建连续路径
    for start_seg in segments:
        if start_seg['used']:
            continue

        # 从当前线段开始构建路径
        current_chain = [start_seg]
        start_seg['used'] = True

        # 向前扩展（从起点向前找）
        current_point = start_seg['start']
        while True:
            found_prev = False
            for seg in segments:
                if seg['used']:
                    continue
                # 找到连接到当前起点的线段的终点
                if points_are_close(seg['end'], current_point, tolerance):
                    current_chain.insert(0, seg)  # 插入到链的开头
                    seg['used'] = True
                    current_point = seg['start']
                    found_prev = True
                    break
                # 找到连接到当前起点的线段的起点（需要反向）
                elif points_are_close(seg['start'], current_point, tolerance):
                    # 反向线段
                    seg['start'], seg['end'] = seg['end'], seg['start']
                    seg['vertices'] = list(reversed(seg['vertices']))
                    current_chain.insert(0, seg)  # 插入到链的开头
                    seg['used'] = True
                    current_point = seg['start']
                    found_prev = True
                    break
            if not found_prev:
                break

        # 向后扩展（从终点向后找）
        current_point = start_seg['end']
        while True:
            found_next = False
            for seg in segments:
                if seg['used']:
                    continue
                # 找到连接到当前终点的线段的起点
                if points_are_close(seg['start'], current_point, tolerance):
                    current_chain.append(seg)  # 添加到链的末尾
                    seg['used'] = True
                    current_point = seg['end']
                    found_next = True
                    break
                # 找到连接到当前终点的线段的终点（需要反向）
                elif points_are_close(seg['end'], current_point, tolerance):
                    # 反向线段
                    seg['start'], seg['end'] = seg['end'], seg['start']
                    seg['vertices'] = list(reversed(seg['vertices']))
                    current_chain.append(seg)  # 添加到链的末尾
                    seg['used'] = True
                    current_point = seg['end']
                    found_next = True
                    break
            if not found_next:
                break

        # 将构建的链添加到组中
        groups.append(current_chain)

    return groups


def create_merged_polyline(doc, segments, layer_name):
    """创建合并后的多段线"""
    # 收集所有顶点，新算法已确保正确顺序
    all_vertices = []

    if len(segments) == 1:
        # 只有一个线段，直接使用其顶点
        all_vertices = segments[0]['vertices']
    else:
        # 多个线段，已经按正确顺序排列，直接合并顶点
        for i, seg in enumerate(segments):
            if i == 0:
                # 第一个线段，添加所有顶点
                all_vertices.extend(seg['vertices'])
            else:
                # 后续线段，跳过第一个顶点（避免重复）
                all_vertices.extend(seg['vertices'][1:])

    # 创建顶点数组（只包含X和Y坐标）
    vertices_flat = []
    for vertex in all_vertices:
        vertices_flat.extend([vertex[0], vertex[1]])

    # 创建新的多段线
    try:
        # 转换为VARIANT数组
        vertices_array = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_R8,
            vertices_flat
        )

        # 在模型空间中添加轻量多段线
        new_polyline = doc.ModelSpace.AddLightWeightPolyline(vertices_array)
        new_polyline.Layer = layer_name

        # 检查原始线段是否闭合
        if len(segments) > 0 and segments[0]['polyline'].Closed:
            new_polyline.Closed = True

        return new_polyline
    except Exception as e:
        print(f"创建多段线时出错: {str(e)}")
        return None


def merge_polylines_in_layer(dwg_file_path, layer_name="450301", tolerance=0.001):
    """合并指定图层中被打断的多段线"""

    print(f"=" * 80)
    print(f"合并图层 '{layer_name}' 中的多段线")
    print(f"=" * 80)
    print(f"DWG文件: {dwg_file_path}")
    print(f"容差: {tolerance}")
    print(f"=" * 80)

    # 检查文件是否存在
    if not os.path.exists(dwg_file_path):
        print(f"错误：文件不存在 - {dwg_file_path}")
        return

    acad = None
    doc = None

    try:
        # 连接AutoCAD
        print("\n正在连接AutoCAD...")
        acad = win32com.client.Dispatch("AutoCAD.Application")
        acad.Visible = True
        time.sleep(1)
        print("✓ 连接成功")

        # 打开文件
        print("\n正在打开文件...")
        doc = acad.Documents.Open(dwg_file_path)
        time.sleep(2)
        doc = acad.ActiveDocument
        print(f"✓ 文件打开成功: {doc.Name}")

        # 创建选择集获取图层中的所有多段线
        ss_name = f"Merge_{layer_name}_{int(time.time() * 1000)}"

        # 删除同名选择集
        try:
            doc.SelectionSets.Item(ss_name).Delete()
        except:
            pass

        sset = doc.SelectionSets.Add(ss_name)

        # 创建过滤器 - 选择指定图层的多段线
        filter_type = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_I2,
            [0, 8]  # 0=对象类型, 8=图层
        )
        filter_data = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_VARIANT,
            ["LWPOLYLINE", layer_name]
        )

        # 选择对象
        sset.Select(5, None, None, filter_type, filter_data)  # 5 = acSelectionSetAll

        print(f"\n找到 {sset.Count} 个多段线对象")

        # 收集所有多段线
        polylines = []
        for i in range(sset.Count):
            polylines.append(sset.Item(i))

        # 查找连通的线段组
        print("\n分析线段连通性...")
        connected_groups = find_connected_segments(polylines, tolerance)

        print(f"\n发现 {len(connected_groups)} 组连通的线段")
        for i, group in enumerate(connected_groups):
            print(f"  第 {i + 1} 组: {len(group)} 个线段")

        # 创建合并后的多段线
        print("\n开始合并线段...")
        new_polylines = []

        for i, group in enumerate(connected_groups):
            print(f"\n处理第 {i + 1} 组...")

            if len(group) == 1:
                # 只有一个线段，不需要合并
                print(f"  该组只有1个线段，跳过")
                continue

            # 创建合并后的多段线
            new_polyline = create_merged_polyline(doc, group, layer_name)

            if new_polyline:
                new_polylines.append(new_polyline)
                print(f"  ✓ 成功创建合并后的多段线")

                # 删除原始线段
                for seg in group:
                    try:
                        seg['polyline'].Delete()
                    except:
                        pass
                print(f"  ✓ 删除了 {len(group)} 个原始线段")
            else:
                print(f"  ✗ 合并失败")

        # 刷新显示
        doc.Regen(1)  # acActiveViewport

        # 统计结果
        print(f"\n" + "=" * 80)
        print(f"合并完成！")
        print(f"原始线段数: {len(polylines)}")
        print(f"合并后的多段线数: {len(new_polylines)}")
        print(f"保持不变的线段数: {sum(1 for g in connected_groups if len(g) == 1)}")
        print(f"=" * 80)

        # 删除选择集
        try:
            sset.Delete()
        except:
            pass

        # 保存文件（可选）
        save_choice = input("\n是否保存更改？(y/n): ")
        if save_choice.lower() == 'y':
            doc.Save()
            print("✓ 文件已保存")
        else:
            print("✗ 未保存更改")

    except Exception as e:
        print(f"\n✗ 出错: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理COM对象引用
        doc = None
        acad = None


# 主程序
if __name__ == "__main__":
    # 文件路径
    dwg_file_path = r"D:\myWork\2025\0805_桥路处理\data\铁三院0805.dwg"

    # 要处理的图层名称
    layer_name = "450301"

    # 容差值（判断两点是否重合的距离阈值）
    tolerance = 0.1

    # 执行合并操作
    merge_polylines_in_layer(dwg_file_path, layer_name, tolerance)

    input("\n按Enter键退出...")